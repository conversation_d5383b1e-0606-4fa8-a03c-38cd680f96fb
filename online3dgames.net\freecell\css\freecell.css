/* FreeCell Specific Styles */

.freecell-board {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 30px;
    position: relative;
    min-height: 70vh;
}

/* Top Area Layout */
.top-area {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 20px;
}

/* Free Cells Area */
.freecells-area {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.freecell-pile {
    width: 100px;
    height: 145px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.freecell-pile .pile-placeholder {
    color: rgba(255,255,255,0.5);
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

/* Remove hover effects for cleaner experience */

/* Foundation Area */
.foundation-area {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.foundation-pile {
    width: 100px;
    height: 145px;
    border: 2px dashed rgba(255,255,255,0.3);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.foundation-pile .pile-placeholder {
    color: rgba(255,255,255,0.5);
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}

/* Remove foundation hover effects */

.foundation-pile[data-suit="hearts"] .pile-placeholder,
.foundation-pile[data-suit="diamonds"] .pile-placeholder {
    color: rgba(220, 20, 60, 0.7);
}

.foundation-pile[data-suit="clubs"] .pile-placeholder,
.foundation-pile[data-suit="spades"] .pile-placeholder {
    color: rgba(0, 0, 0, 0.7);
}

/* Tableau Area */
.freecell-tableau {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    flex: 1;
}

.freecell-tableau .tableau-pile {
    width: auto;
    min-width: 100px;
    min-height: 145px;
    border: 2px dashed rgba(255,255,255,0.2);
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Remove tableau hover effects */

/* Card positioning in tableau */
.tableau-pile .card {
    position: relative !important;
    margin-top: -120px;
    z-index: 1;
}

.tableau-pile .card:first-child {
    margin-top: 10px;
}

.tableau-pile .card:nth-child(n+2) {
    margin-top: -120px;
}

/* Better card spacing for readability */
.tableau-pile .card:nth-child(n+2) {
    margin-top: -115px; /* Slightly more spacing */
}

/* Card Selection and Highlighting */
.card.selected {
    box-shadow: 0 0 0 2px #ffeb3b, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-2px);
}

.card.multi-selected {
    box-shadow: 0 0 0 2px #4CAF50, 0 4px 8px rgba(0,0,0,0.3);
    transform: translateY(-1px);
}

/* Drag and Drop States */
.card.dragging {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 8px 20px rgba(0,0,0,0.5);
    transform: scale(1.05) rotate(1deg);
    transition: none !important;
}

.card.dragging-multi {
    z-index: 9999 !important;
    pointer-events: none;
    position: fixed !important;
    box-shadow: 0 15px 30px rgba(0,0,0,0.7), 0 0 0 3px #ffeb3b;
    transform: scale(1.08) rotate(2deg);
    transition: none !important;
    filter: brightness(1.1);
}

.card.dragging-multi + .card.dragging-multi {
    z-index: 9998 !important;
    box-shadow: 0 12px 24px rgba(0,0,0,0.6), 0 0 0 2px #4CAF50;
    transform: scale(1.05) rotate(1deg);
    filter: brightness(1.05);
}

/* Drop Zone Highlighting */
.drop-zone-valid {
    background: rgba(76, 175, 80, 0.25) !important;
    border: 3px solid rgba(76, 175, 80, 0.8) !important;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6) !important;
    transform: scale(1.02);
    transition: all 0.2s ease;
}

.drop-zone-invalid {
    background: rgba(244, 67, 54, 0.15) !important;
    border: 3px solid rgba(244, 67, 54, 0.6) !important;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.4) !important;
    animation: invalidShake 0.3s ease-in-out;
}

@keyframes invalidShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Card Animations */
.card.flying {
    z-index: 10000 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.4s linear !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    will-change: transform, left, top;
}

.card.flying-to-foundation {
    z-index: 10001 !important;
    pointer-events: none;
    position: fixed !important;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    box-shadow: 0 12px 30px rgba(255, 215, 0, 0.6);
    transform: scale(1.1);
    will-change: transform, left, top;
}

.card.placing {
    animation: placeCard 0.3s ease-out;
}

@keyframes placeCard {
    0% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Hint Animation */
.card.hint-highlight {
    animation: hintPulse 1s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 0 2px transparent;
    }
    50% {
        box-shadow: 0 0 0 2px #ffeb3b, 0 0 10px rgba(255, 235, 59, 0.5);
    }
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .top-area {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .freecells-area,
    .foundation-area {
        gap: 8px;
    }

    .freecell-pile,
    .foundation-pile,
    .card {
        width: 80px;
        height: 116px;
        font-size: 14px;
    }

    .card-center {
        font-size: 24px;
    }

    .freecell-tableau {
        gap: 8px;
        grid-template-columns: repeat(8, 1fr);
    }

    .tableau-pile .card {
        margin-top: -92px; /* Better spacing for mobile */
    }

    .tableau-pile .card:first-child {
        margin-top: 8px;
    }

    .tableau-pile .card:nth-child(n+2) {
        margin-top: -92px;
    }
}

@media (max-width: 768px) {
    .top-area {
        gap: 10px;
    }

    .freecells-area,
    .foundation-area {
        gap: 5px;
    }

    .freecell-pile,
    .foundation-pile,
    .card {
        width: 70px;
        height: 101px;
        font-size: 12px;
    }

    .card-center {
        font-size: 20px;
    }

    .freecell-tableau {
        gap: 5px;
    }

    .tableau-pile .card {
        margin-top: -80px; /* Better spacing for small screens */
    }

    .tableau-pile .card:first-child {
        margin-top: 5px;
    }

    .tableau-pile .card:nth-child(n+2) {
        margin-top: -80px;
    }

    .card.flying,
    .card.flying-to-foundation {
        transition: all 0.3s ease-out !important;
    }

    .card.dragging {
        transform: scale(1.05);
        z-index: 1000;
        opacity: 0.95;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .card.dragging-multi {
        transform: scale(1.08);
        z-index: 1001;
        opacity: 0.95;
        box-shadow: 0 6px 12px rgba(0,0,0,0.4);
    }
}

/* Performance Optimizations */
.card {
    will-change: transform, opacity, box-shadow;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .card,
    .card.flying,
    .card.flying-to-foundation,
    .card.dragging,
    .card.dragging-multi {
        transition: none !important;
        animation: none !important;
    }
}

/* Auto-complete Animation */
.card.auto-completing {
    animation: autoComplete 0.6s ease-out forwards;
}

@keyframes autoComplete {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(0.9);
        opacity: 0.8;
    }
}
