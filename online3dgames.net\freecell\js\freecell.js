class FreeCell {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.freeCells = [null, null, null, null]; // 4 free cells
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] }; // 4 foundation piles
        this.tableau = [[], [], [], [], [], [], [], []]; // 8 tableau columns
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];

        this.comboCount = 0;
        this.lastMoveTime = 0;
        this.comboTimeWindow = 10000;
        this.maxComboMultiplier = 5;

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = this.isMobile() ? 12 : 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        this.suits = ['spades', 'hearts', 'clubs', 'diamonds'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.isAnimatingCard = false;
        this.isAutoCompleting = false;
        this.isProcessingAction = false;
        this.lastActionTime = 0;
        this.actionCooldown = 100;
        this.buttonCooldowns = new Map();

        if (!testMode) {
            this.bindEvents();
        } else {
            this.initializeGame();
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }

    // Create a standard 52-card deck
    createDeck() {
        this.deck = [];
        for (const suit of this.suits) {
            for (const rank of this.ranks) {
                this.deck.push({
                    suit: suit,
                    rank: rank,
                    color: this.suitColors[suit],
                    value: this.getCardValue(rank),
                    id: `${rank}_${suit}`
                });
            }
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    // Shuffle the deck using Fisher-Yates algorithm
    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    // Deal cards for FreeCell (all face up)
    dealCards() {
        this.createDeck();
        this.shuffleDeck();

        // Clear all areas
        this.freeCells = [null, null, null, null];
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] };
        this.tableau = [[], [], [], [], [], [], [], []];

        // Deal cards to tableau: first 4 columns get 7 cards, last 4 get 6 cards
        let cardIndex = 0;
        for (let col = 0; col < 8; col++) {
            const cardsInColumn = col < 4 ? 7 : 6;
            for (let row = 0; row < cardsInColumn; row++) {
                this.tableau[col].push(this.deck[cardIndex]);
                cardIndex++;
            }
        }
    }

    // Initialize a new game
    initializeGame() {
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.startTime = Date.now();
        this.comboCount = 0;
        this.lastMoveTime = 0;

        this.dealCards();
        this.renderGame();
        this.startTimer();
        this.updateStats();
    }

    // Render the entire game state
    renderGame() {
        this.renderFreeCells();
        this.renderFoundations();
        this.renderTableau();
    }

    // Render free cells
    renderFreeCells() {
        for (let i = 0; i < 4; i++) {
            const $pile = $(`#freecell-${i}`);
            $pile.empty();
            
            if (this.freeCells[i]) {
                const $card = this.createCardElement(this.freeCells[i]);
                $pile.append($card);
            } else {
                $pile.append('<div class="pile-placeholder">Free</div>');
            }
        }
    }

    // Render foundation piles
    renderFoundations() {
        for (const suit of this.suits) {
            const $pile = $(`#foundation-${suit}`);
            $pile.empty();
            
            const cards = this.foundations[suit];
            if (cards.length > 0) {
                const topCard = cards[cards.length - 1];
                const $card = this.createCardElement(topCard);
                $pile.append($card);
            } else {
                $pile.append(`<div class="pile-placeholder">${this.suitSymbols[suit]}</div>`);
            }
        }
    }

    // Render tableau columns
    renderTableau() {
        for (let col = 0; col < 8; col++) {
            const $pile = $(`#tableau-${col}`);
            $pile.empty();
            
            const cards = this.tableau[col];
            cards.forEach((card, index) => {
                const $card = this.createCardElement(card);
                $card.css({
                    top: `${index * 25}px`,
                    left: '16px',
                    zIndex: index + 1
                });
                $pile.append($card);
            });
        }
    }

    // Create a card DOM element
    createCardElement(card) {
        const $card = $('<div>')
            .addClass('card draggable-card')
            .addClass(card.color)
            .attr('data-card-id', card.id)
            .attr('data-suit', card.suit)
            .attr('data-rank', card.rank)
            .attr('data-value', card.value);

        const $top = $('<div>').addClass('card-top');
        $top.append(`<span>${card.rank}</span>`);
        $top.append(`<span>${this.suitSymbols[card.suit]}</span>`);

        const $center = $('<div>').addClass('card-center');
        $center.text(this.suitSymbols[card.suit]);

        const $bottom = $('<div>').addClass('card-bottom');
        $bottom.append(`<span>${card.rank}</span>`);
        $bottom.append(`<span>${this.suitSymbols[card.suit]}</span>`);

        $card.append($top, $center, $bottom);
        return $card;
    }

    // Start the game timer
    startTimer() {
        if (this.timer) {
            clearInterval(this.timer);
        }
        
        this.timer = setInterval(() => {
            if (!this.gameWon) {
                const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                $('#timer').text(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
            }
        }, 1000);
    }

    // Update game statistics
    updateStats() {
        $('#score').text(this.score);
        $('#moves').text(this.moves);
    }

    // Check if a card can be moved to a foundation pile
    canMoveToFoundation(card, suit) {
        if (card.suit !== suit) return false;
        
        const foundation = this.foundations[suit];
        if (foundation.length === 0) {
            return card.rank === 'A';
        }
        
        const topCard = foundation[foundation.length - 1];
        return card.value === topCard.value + 1;
    }

    // Check if a card can be moved to a tableau column
    canMoveToTableau(card, columnIndex) {
        const column = this.tableau[columnIndex];
        if (column.length === 0) {
            return true; // Any card can go on empty column
        }
        
        const topCard = column[column.length - 1];
        return card.color !== topCard.color && card.value === topCard.value - 1;
    }

    // Check if a sequence of cards can be moved together
    canMoveSequence(cards, targetColumn) {
        if (cards.length === 0) return false;
        
        // Check if the sequence is valid (alternating colors, descending values)
        for (let i = 0; i < cards.length - 1; i++) {
            const current = cards[i];
            const next = cards[i + 1];
            if (current.color === next.color || current.value !== next.value + 1) {
                return false;
            }
        }
        
        // Check if we have enough free cells and empty columns to move this sequence
        const freeCells = this.freeCells.filter(cell => cell === null).length;
        const emptyColumns = this.tableau.filter(col => col.length === 0).length;
        
        // If target is empty, we need one less empty column
        const availableEmptyColumns = this.tableau[targetColumn].length === 0 ? 
            emptyColumns - 1 : emptyColumns;
        
        const maxMovableCards = (freeCells + 1) * Math.pow(2, availableEmptyColumns);
        
        return cards.length <= maxMovableCards;
    }

    // Get the maximum number of cards that can be moved as a sequence
    getMaxMovableSequence(columnIndex) {
        const column = this.tableau[columnIndex];
        if (column.length === 0) return [];
        
        const sequence = [column[column.length - 1]];
        
        for (let i = column.length - 2; i >= 0; i--) {
            const current = column[i];
            const last = sequence[sequence.length - 1];
            
            if (current.color !== last.color && current.value === last.value + 1) {
                sequence.push(current);
            } else {
                break;
            }
        }
        
        return sequence.reverse();
    }

    // Bind event handlers
    bindEvents() {
        $(document).ready(() => {
            this.initializeGame();
            this.setupEventHandlers();
        });
    }

    setupEventHandlers() {
        // Button events
        $('#newGameBtn').on('click', () => this.initializeGame());
        $('#undoBtn').on('click', () => this.undoMove());
        $('#hintBtn').on('click', () => this.showHint());
        $('#helpBtn').on('click', () => this.showHelp());
        $('#homeBtn').on('click', () => window.location.href = '/');
        $('#fullscreenBtn').on('click', () => this.toggleFullscreen());
        
        // Game message events
        $('#playAgainBtn').on('click', () => {
            this.hideGameMessage();
            this.initializeGame();
        });
        $('#closeMessageBtn').on('click', () => this.hideGameMessage());
        
        // Help panel events
        $('#closeHelpBtn, #closeHelpBtnBottom').on('click', () => this.hideHelp());
        
        // Card drag and drop events
        this.setupDragAndDrop();
        
        // Double-click for auto-move
        $(document).on('dblclick', '.card', (e) => this.handleCardDoubleClick(e));
    }

    // Setup drag and drop functionality
    setupDragAndDrop() {
        let startX, startY, hasMoved = false;

        $(document).on('mousedown touchstart', '.card', (e) => {
            if (this.isAnimatingCard || this.isProcessingAction) return;

            e.preventDefault();
            const touch = e.type === 'touchstart' ? e.originalEvent.touches[0] : e;
            startX = touch.clientX;
            startY = touch.clientY;
            hasMoved = false;

            this.startDrag(e);
        });

        $(document).on('mousemove touchmove', (e) => {
            if (!this.isDragging) return;

            e.preventDefault();
            const touch = e.type === 'touchmove' ? e.originalEvent.touches[0] : e;

            if (!hasMoved) {
                const deltaX = Math.abs(touch.clientX - startX);
                const deltaY = Math.abs(touch.clientY - startY);
                if (deltaX > this.dragThreshold || deltaY > this.dragThreshold) {
                    hasMoved = true;
                }
            }

            if (hasMoved) {
                this.updateDrag(e);
            }
        });

        $(document).on('mouseup touchend', (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.endDrag(e);
            }
        });
    }

    // Start dragging a card or sequence
    startDrag(e) {
        const $card = $(e.target).closest('.card');
        if (!$card.length) return;

        const cardData = this.getCardData($card);
        const sourceInfo = this.getCardSource($card);

        if (!sourceInfo) return;

        // Determine what cards to drag
        let cardsToDrag = [cardData];

        if (sourceInfo.type === 'tableau') {
            const sequence = this.getMaxMovableSequence(sourceInfo.index);
            const cardIndex = sequence.findIndex(c => c.id === cardData.id);
            if (cardIndex >= 0) {
                cardsToDrag = sequence.slice(cardIndex);
            }
        }

        this.draggedCards = cardsToDrag;
        this.draggedFrom = sourceInfo;
        this.isDragging = true;
        this.justFinishedDrag = false;

        // Create drag elements
        this.draggedElements = cardsToDrag.map((card, index) => {
            const $element = this.createCardElement(card);
            $element.addClass('dragging');
            $element.css({
                position: 'fixed',
                zIndex: 9999 - index,
                pointerEvents: 'none'
            });
            $('body').append($element);
            return $element;
        });

        const touch = e.type === 'touchstart' ? e.originalEvent.touches[0] : e;
        this.dragOffset = {
            x: touch.clientX,
            y: touch.clientY
        };

        this.updateDragPosition(touch.clientX, touch.clientY);
    }

    // Update drag position
    updateDrag(e) {
        if (!this.isDragging || !this.draggedElements) return;

        const touch = e.type === 'touchmove' ? e.originalEvent.touches[0] : e;
        this.updateDragPosition(touch.clientX, touch.clientY);
        this.updateDropZones(touch.clientX, touch.clientY);
    }

    // Update the position of dragged elements
    updateDragPosition(x, y) {
        if (!this.draggedElements) return;

        this.draggedElements.forEach(($element, index) => {
            const cardWidth = $element.outerWidth();
            const cardHeight = $element.outerHeight();

            $element.css({
                left: (x - cardWidth / 2) + 'px',
                top: (y - cardHeight / 2 + index * 25) + 'px'
            });
        });
    }

    // Update drop zone visual feedback
    updateDropZones(x, y) {
        $('.drop-zone-valid, .drop-zone-invalid').removeClass('drop-zone-valid drop-zone-invalid');

        const elementBelow = document.elementFromPoint(x, y);
        const $target = $(elementBelow).closest('.freecell-pile, .foundation-pile, .tableau-pile');

        if ($target.length) {
            const isValid = this.isValidDrop($target);
            $target.addClass(isValid ? 'drop-zone-valid' : 'drop-zone-invalid');
        }
    }

    // Check if the current drag is a valid drop
    isValidDrop($target) {
        if (!this.draggedCards || this.draggedCards.length === 0) return false;

        const firstCard = this.draggedCards[0];

        if ($target.hasClass('freecell-pile')) {
            // Can only move single cards to free cells
            if (this.draggedCards.length > 1) return false;
            const index = parseInt($target.attr('id').split('-')[1]);
            return this.freeCells[index] === null;
        }

        if ($target.hasClass('foundation-pile')) {
            // Can only move single cards to foundations
            if (this.draggedCards.length > 1) return false;
            const suit = $target.attr('data-suit');
            return this.canMoveToFoundation(firstCard, suit);
        }

        if ($target.hasClass('tableau-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            return this.canMoveSequence(this.draggedCards, index) &&
                   this.canMoveToTableau(firstCard, index);
        }

        return false;
    }

    // End the drag operation
    endDrag(e) {
        if (!this.isDragging) return;

        const touch = e.type === 'touchend' ? e.originalEvent.changedTouches[0] : e;
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        const $target = $(elementBelow).closest('.freecell-pile, .foundation-pile, .tableau-pile');

        let moveSuccessful = false;

        if ($target.length && this.isValidDrop($target)) {
            moveSuccessful = this.performMove($target);
        }

        // Clean up drag elements
        if (this.draggedElements) {
            this.draggedElements.forEach($element => $element.remove());
        }

        // Clean up drop zone styling
        $('.drop-zone-valid, .drop-zone-invalid').removeClass('drop-zone-valid drop-zone-invalid');

        // Reset drag state
        this.isDragging = false;
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElements = null;
        this.justFinishedDrag = true;

        if (moveSuccessful) {
            this.renderGame();
            this.updateStats();
            this.checkForWin();
            this.tryAutoComplete();
        }

        // Reset the flag after a short delay
        setTimeout(() => {
            this.justFinishedDrag = false;
        }, 100);
    }

    // Perform the actual move
    performMove($target) {
        if (!this.draggedCards || !this.draggedFrom) return false;

        const firstCard = this.draggedCards[0];

        // Remove cards from source
        this.removeCardsFromSource();

        // Add cards to target
        if ($target.hasClass('freecell-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            this.freeCells[index] = firstCard;
        } else if ($target.hasClass('foundation-pile')) {
            const suit = $target.attr('data-suit');
            this.foundations[suit].push(firstCard);
            this.score += 10; // Points for moving to foundation
        } else if ($target.hasClass('tableau-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            this.tableau[index].push(...this.draggedCards);
        }

        // Record move for undo
        this.recordMove();
        this.moves++;
        this.score += 1; // Point for any move

        return true;
    }

    // Remove cards from their source location
    removeCardsFromSource() {
        if (!this.draggedFrom || !this.draggedCards) return;

        if (this.draggedFrom.type === 'freecell') {
            this.freeCells[this.draggedFrom.index] = null;
        } else if (this.draggedFrom.type === 'foundation') {
            const suit = this.draggedFrom.suit;
            this.foundations[suit].pop();
        } else if (this.draggedFrom.type === 'tableau') {
            const column = this.tableau[this.draggedFrom.index];
            column.splice(column.length - this.draggedCards.length);
        }
    }

    // Get card data from DOM element
    getCardData($card) {
        return {
            id: $card.attr('data-card-id'),
            suit: $card.attr('data-suit'),
            rank: $card.attr('data-rank'),
            value: parseInt($card.attr('data-value')),
            color: this.suitColors[$card.attr('data-suit')]
        };
    }

    // Get the source location of a card
    getCardSource($card) {
        const $pile = $card.closest('.freecell-pile, .foundation-pile, .tableau-pile');

        if ($pile.hasClass('freecell-pile')) {
            const index = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'freecell', index: index };
        }

        if ($pile.hasClass('foundation-pile')) {
            const suit = $pile.attr('data-suit');
            return { type: 'foundation', suit: suit };
        }

        if ($pile.hasClass('tableau-pile')) {
            const index = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'tableau', index: index };
        }

        return null;
    }

    // Handle double-click for auto-move
    handleCardDoubleClick(e) {
        if (this.justFinishedDrag || this.isAnimatingCard || this.isProcessingAction) return;

        const $card = $(e.target).closest('.card');
        if (!$card.length) return;

        const cardData = this.getCardData($card);
        const sourceInfo = this.getCardSource($card);

        if (!sourceInfo) return;

        // Try to move to foundation first
        for (const suit of this.suits) {
            if (this.canMoveToFoundation(cardData, suit)) {
                this.performAutoMove(cardData, sourceInfo, { type: 'foundation', suit: suit });
                return;
            }
        }

        // Try to move to an empty free cell
        for (let i = 0; i < 4; i++) {
            if (this.freeCells[i] === null) {
                this.performAutoMove(cardData, sourceInfo, { type: 'freecell', index: i });
                return;
            }
        }

        // Try to move to tableau
        for (let i = 0; i < 8; i++) {
            if (this.canMoveToTableau(cardData, i)) {
                this.performAutoMove(cardData, sourceInfo, { type: 'tableau', index: i });
                return;
            }
        }
    }

    // Perform an automatic move
    performAutoMove(cardData, sourceInfo, targetInfo) {
        // Remove from source
        if (sourceInfo.type === 'freecell') {
            this.freeCells[sourceInfo.index] = null;
        } else if (sourceInfo.type === 'foundation') {
            this.foundations[sourceInfo.suit].pop();
        } else if (sourceInfo.type === 'tableau') {
            this.tableau[sourceInfo.index].pop();
        }

        // Add to target
        if (targetInfo.type === 'freecell') {
            this.freeCells[targetInfo.index] = cardData;
        } else if (targetInfo.type === 'foundation') {
            this.foundations[targetInfo.suit].push(cardData);
            this.score += 10;
        } else if (targetInfo.type === 'tableau') {
            this.tableau[targetInfo.index].push(cardData);
        }

        this.recordMove();
        this.moves++;
        this.score += 1;

        this.renderGame();
        this.updateStats();
        this.checkForWin();
        this.tryAutoComplete();
    }

    // Record a move for undo functionality
    recordMove() {
        const gameState = {
            freeCells: [...this.freeCells],
            foundations: {
                hearts: [...this.foundations.hearts],
                diamonds: [...this.foundations.diamonds],
                clubs: [...this.foundations.clubs],
                spades: [...this.foundations.spades]
            },
            tableau: this.tableau.map(col => [...col]),
            score: this.score,
            moves: this.moves
        };

        this.moveHistory.push(gameState);

        // Limit history to prevent memory issues
        if (this.moveHistory.length > 100) {
            this.moveHistory.shift();
        }
    }

    // Undo the last move
    undoMove() {
        if (this.moveHistory.length === 0 || this.isAnimatingCard || this.isProcessingAction) return;

        const previousState = this.moveHistory.pop();

        this.freeCells = previousState.freeCells;
        this.foundations = previousState.foundations;
        this.tableau = previousState.tableau;
        this.score = previousState.score;
        this.moves = previousState.moves;

        this.renderGame();
        this.updateStats();
    }

    // Try to automatically complete obvious moves
    tryAutoComplete() {
        if (this.isAutoCompleting) return;

        this.isAutoCompleting = true;
        let madeMove = false;

        // Try to move low cards to foundations
        for (const suit of this.suits) {
            const foundation = this.foundations[suit];
            const nextValue = foundation.length + 1;

            // Only auto-complete if the card is safe to move (low value)
            if (nextValue <= 3) {
                // Check tableau columns
                for (let col = 0; col < 8; col++) {
                    const column = this.tableau[col];
                    if (column.length > 0) {
                        const topCard = column[column.length - 1];
                        if (topCard.suit === suit && topCard.value === nextValue) {
                            this.performAutoMove(topCard,
                                { type: 'tableau', index: col },
                                { type: 'foundation', suit: suit }
                            );
                            madeMove = true;
                            break;
                        }
                    }
                }

                // Check free cells
                if (!madeMove) {
                    for (let i = 0; i < 4; i++) {
                        const card = this.freeCells[i];
                        if (card && card.suit === suit && card.value === nextValue) {
                            this.performAutoMove(card,
                                { type: 'freecell', index: i },
                                { type: 'foundation', suit: suit }
                            );
                            madeMove = true;
                            break;
                        }
                    }
                }
            }

            if (madeMove) break;
        }

        this.isAutoCompleting = false;

        // If we made a move, try again after a short delay
        if (madeMove) {
            setTimeout(() => this.tryAutoComplete(), 500);
        }
    }

    // Check if the game is won
    checkForWin() {
        const totalFoundationCards = Object.values(this.foundations)
            .reduce((sum, pile) => sum + pile.length, 0);

        if (totalFoundationCards === 52) {
            this.gameWon = true;
            this.score += 500; // Bonus for winning
            clearInterval(this.timer);
            this.showWinMessage();
        }
    }

    // Show win message
    showWinMessage() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        $('#messageTitle').text('Congratulations!');
        $('#messageText').text('You won the game!');
        $('#finalScore').text(this.score);
        $('#finalTime').text(timeStr);
        $('#finalMoves').text(this.moves);
        $('#gameMessage').removeClass('hidden');
    }

    // Hide game message
    hideGameMessage() {
        $('#gameMessage').addClass('hidden');
    }

    // Show hint
    showHint() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        // Clear previous hints
        $('.card-hint, .pile-hint').removeClass('card-hint pile-hint');

        // Look for possible moves
        let hintFound = false;

        // Check for moves to foundation
        for (const suit of this.suits) {
            if (hintFound) break;

            // Check tableau
            for (let col = 0; col < 8; col++) {
                const column = this.tableau[col];
                if (column.length > 0) {
                    const topCard = column[column.length - 1];
                    if (this.canMoveToFoundation(topCard, suit)) {
                        $(`#tableau-${col} .card:last`).addClass('card-hint');
                        $(`#foundation-${suit}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }

            // Check free cells
            if (!hintFound) {
                for (let i = 0; i < 4; i++) {
                    const card = this.freeCells[i];
                    if (card && this.canMoveToFoundation(card, suit)) {
                        $(`#freecell-${i} .card`).addClass('card-hint');
                        $(`#foundation-${suit}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }
        }

        // If no foundation moves, look for tableau moves
        if (!hintFound) {
            for (let sourceCol = 0; sourceCol < 8; sourceCol++) {
                if (hintFound) break;

                const sourceColumn = this.tableau[sourceCol];
                if (sourceColumn.length === 0) continue;

                const sequence = this.getMaxMovableSequence(sourceCol);
                if (sequence.length === 0) continue;

                for (let targetCol = 0; targetCol < 8; targetCol++) {
                    if (sourceCol === targetCol) continue;

                    if (this.canMoveSequence(sequence, targetCol) &&
                        this.canMoveToTableau(sequence[0], targetCol)) {
                        $(`#tableau-${sourceCol} .card:last`).addClass('card-hint');
                        $(`#tableau-${targetCol}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }
        }

        // Clear hints after 3 seconds
        setTimeout(() => {
            $('.card-hint, .pile-hint').removeClass('card-hint pile-hint');
        }, 3000);
    }

    // Show help panel
    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    // Hide help panel
    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    // Toggle fullscreen
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }
}

// Initialize the game when the page loads
$(document).ready(() => {
    window.freeCellGame = new FreeCell();
});
