class FreeCellSolitaire {
    constructor() {
        this.deck = [];
        this.tableau = [[], [], [], [], [], [], [], []];
        this.freeCells = [null, null, null, null];
        this.foundation = { hearts: [], diamonds: [], clubs: [], spades: [] };

        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];

        this.draggedCards = [];
        this.draggedFrom = null;
        this.isDragging = false;
        this.dragStartPos = { x: 0, y: 0 };
        this.dragOffset = { x: 0, y: 0 };
        this.isAnimatingCard = false;
        this.isProcessingAction = false;

        this.suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };

        this.bindEvents();
        this.initializeGame();
    }

    bindEvents() {
        $('#newGameBtn').on('click', () => this.newGame());
        $('#undoBtn').on('click', () => this.undoMove());
        $('#hintBtn').on('click', () => this.showHint());
        $('#helpBtn').on('click', () => this.showHelp());
        $('#fullscreenBtn').on('click', () => this.toggleFullscreen());
        $('#homeBtn').on('click', () => window.location.href = '/');
        $('#playAgainBtn').on('click', () => this.newGame());
        $('#closeMessageBtn').on('click', () => this.hideMessage());
        $('#closeHelpBtn, #closeHelpBtnBottom').on('click', () => this.hideHelp());

        $(document).on('mousedown touchstart', '.card', (e) => this.onPointerDown(e));
        $(document).on('mousemove touchmove', (e) => this.onPointerMove(e));
        $(document).on('mouseup touchend', (e) => this.onPointerUp(e));
        $(document).on('dblclick', '.card', (e) => this.onCardDoubleClick(e));
        $(document).on('click', '.freecell-pile, .foundation-pile, .tableau-pile', (e) => this.onPileClick(e));
        $(document).on('keydown', (e) => this.onKeyDown(e));
        $(document).on('contextmenu', '.card', (e) => e.preventDefault());
    }

    initializeGame() {
        this.deck = this.createDeck();
        this.shuffleArray(this.deck);
        this.dealCards();
        this.updateDisplay();
        this.startTimer();
    }

    createDeck() {
        let idCounter = 1;
        const deck = [];
        for (let suit of this.suits) {
            for (let rank of this.ranks) {
                deck.push({
                    id: idCounter++,
                    suit,
                    rank,
                    value: this.getCardValue(rank),
                    color: this.suitColors[suit],
                    faceUp: true
                });
            }
        }
        return deck;
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    dealCards() {
        this.tableau = [[], [], [], [], [], [], [], []];
        this.freeCells = [null, null, null, null];
        this.foundation = { hearts: [], diamonds: [], clubs: [], spades: [] };

        let cardIndex = 0;
        for (let col = 0; col < 8; col++) {
            const cardsInColumn = col < 4 ? 7 : 6;
            for (let row = 0; row < cardsInColumn; row++) {
                if (cardIndex < this.deck.length) {
                    this.tableau[col].push(this.deck[cardIndex]);
                    cardIndex++;
                }
            }
        }
    }

    updateDisplay() {
        this.updateTableau();
        this.updateFreeCells();
        this.updateFoundation();
        this.updateStats();
        this.updateButtonStates();
    }

    updateTableau() {
        for (let i = 0; i < 8; i++) {
            const $pile = $(`#tableau-${i}`);
            $pile.empty();

            this.tableau[i].forEach((card, index) => {
                const $card = this.createCardElement(card);
                $card.css({
                    position: 'relative',
                    top: `${index * 25}px`,
                    zIndex: index + 1
                });
                $pile.append($card);
            });
        }
    }

    updateFreeCells() {
        for (let i = 0; i < 4; i++) {
            const $pile = $(`#freecell-${i}`);
            $pile.find('.card').remove();

            if (this.freeCells[i]) {
                const $card = this.createCardElement(this.freeCells[i]);
                $card.css({ position: 'absolute', top: '0', left: '0', zIndex: 1 });
                $pile.append($card);
            }
        }
    }

    updateFoundation() {
        for (let suit of this.suits) {
            const $pile = $(`#foundation-${suit}`);
            $pile.find('.card').remove();

            const cards = this.foundation[suit];
            if (cards.length > 0) {
                const topCard = cards[cards.length - 1];
                const $card = this.createCardElement(topCard);
                $card.css({ position: 'absolute', top: '0', left: '0', zIndex: 1 });
                $pile.append($card);
            }
        }
    }

    createCardElement(card) {
        const $card = $('<div>')
            .addClass('card draggable-card')
            .addClass(card.color)
            .attr('data-id', card.id)
            .attr('data-suit', card.suit)
            .attr('data-rank', card.rank);

        const symbol = this.suitSymbols[card.suit];

        $card.html(`
            <div class="card-top">
                <span>${card.rank}</span>
                <span>${symbol}</span>
            </div>
            <div class="card-center">${symbol}</div>
            <div class="card-bottom">
                <span>${card.rank}</span>
                <span>${symbol}</span>
            </div>
        `);

        return $card;
    }

    updateStats() {
        $('#score').text(this.score);
        $('#moves').text(this.moves);

        if (this.startTime) {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            $('#timer').text(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
        }
    }

    updateButtonStates() {
        $('#undoBtn').prop('disabled', this.moveHistory.length === 0);
    }

    startTimer() {
        this.startTime = Date.now();
        this.timer = setInterval(() => this.updateStats(), 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    newGame() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        this.isProcessingAction = true;
        this.stopTimer();
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.resetDragState();

        $('.card').removeClass('dragging selected multi-selected hint-highlight');
        $('.card').css({ left: '', top: '', position: '', 'z-index': '', 'transition': '' });

        this.hideMessage();
        this.hideHelp();
        this.initializeGame();

        this.isProcessingAction = false;
    }

    resetDragState() {
        this.isDragging = false;
        this.draggedCards = [];
        this.draggedFrom = null;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };

        $('.card').removeClass('dragging selected multi-selected dragging-multi');
        $('.card').css({
            position: '', left: '', top: '', zIndex: '', transform: '',
            opacity: '', transition: '', pointerEvents: ''
        });
        $('.pile').removeClass('drop-zone-valid drop-zone-invalid');
    }

    onPointerDown(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        e.preventDefault();
        const $card = $(e.currentTarget);
        const cardId = parseInt($card.attr('data-id'));

        const cardInfo = this.findCard(cardId);
        if (!cardInfo) return;

        if (!this.canMoveCard(cardInfo)) return;

        this.draggedCards = this.getMovableSequence(cardInfo);
        this.draggedFrom = cardInfo.location;

        const coords = this.getEventCoordinates(e);
        this.dragStartPos = { x: coords.clientX, y: coords.clientY };

        const cardRect = $card[0].getBoundingClientRect();
        this.dragOffset.x = coords.clientX - cardRect.left;
        this.dragOffset.y = coords.clientY - cardRect.top;

        this.draggedCards.forEach(card => {
            $(`.card[data-id="${card.id}"]`).addClass('selected');
        });
    }

    onPointerMove(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;
        if (!this.draggedCards || this.draggedCards.length === 0) return;

        const coords = this.getEventCoordinates(e);
        const deltaX = coords.clientX - this.dragStartPos.x;
        const deltaY = coords.clientY - this.dragStartPos.y;

        if (!this.isDragging) {
            if (Math.abs(deltaX) > 8 || Math.abs(deltaY) > 8) {
                this.startDrag();
            }
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        this.updateDragPosition(coords.clientX, coords.clientY);
    }

    onPointerUp(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        if (this.isDragging) {
            e.preventDefault();
            e.stopPropagation();

            const coords = this.getEventCoordinates(e);
            const dropTarget = this.getDropTarget(coords.clientX, coords.clientY);

            if (dropTarget && this.isValidDrop(dropTarget)) {
                this.executeDrop(dropTarget);
            } else {
                this.returnCards();
            }
        }

        this.resetDragState();
    }

    startDrag() {
        this.isDragging = true;

        this.draggedCards.forEach((card, index) => {
            const $card = $(`.card[data-id="${card.id}"]`);
            const rect = $card[0].getBoundingClientRect();

            $card.addClass(index === 0 ? 'dragging' : 'dragging-multi');
            $card.css({
                position: 'fixed',
                left: rect.left + 'px',
                top: rect.top + 'px',
                zIndex: 9999 + index,
                pointerEvents: 'none',
                transition: 'none'
            });
        });
    }

    updateDragPosition(clientX, clientY) {
        this.draggedCards.forEach((card, index) => {
            const $card = $(`.card[data-id="${card.id}"]`);
            const offset = index * 25;

            $card.css({
                left: (clientX - this.dragOffset.x) + 'px',
                top: (clientY - this.dragOffset.y + offset) + 'px'
            });
        });
    }

    returnCards() {
        this.draggedCards.forEach(card => {
            const $card = $(`.card[data-id="${card.id}"]`);
            $card.css({ transition: 'all 0.3s ease-out' });

            setTimeout(() => {
                $card.removeClass('dragging dragging-multi').css({
                    position: '', left: '', top: '', zIndex: '',
                    transform: '', opacity: '', transition: '', pointerEvents: ''
                });
            }, 300);
        });
    }

    getEventCoordinates(e) {
        if (e.type && e.type.includes('touch')) {
            const touch = e.originalEvent ? e.originalEvent.touches[0] || e.originalEvent.changedTouches[0] : e.touches[0] || e.changedTouches[0];
            return { clientX: touch.clientX, clientY: touch.clientY };
        }
        return { clientX: e.clientX, clientY: e.clientY };
    }

    findCard(cardId) {
        // Search in tableau
        for (let col = 0; col < 8; col++) {
            const index = this.tableau[col].findIndex(card => card.id === cardId);
            if (index !== -1) {
                return {
                    card: this.tableau[col][index],
                    location: { type: 'tableau', column: col, index }
                };
            }
        }

        // Search in free cells
        for (let i = 0; i < 4; i++) {
            if (this.freeCells[i] && this.freeCells[i].id === cardId) {
                return {
                    card: this.freeCells[i],
                    location: { type: 'freecell', index: i }
                };
            }
        }

        // Search in foundation
        for (let suit of this.suits) {
            const index = this.foundation[suit].findIndex(card => card.id === cardId);
            if (index !== -1) {
                return {
                    card: this.foundation[suit][index],
                    location: { type: 'foundation', suit, index }
                };
            }
        }

        return null;
    }

    canMoveCard(cardInfo) {
        const { location } = cardInfo;

        if (location.type === 'foundation') return false;
        if (location.type === 'freecell') return true;

        if (location.type === 'tableau') {
            const column = this.tableau[location.column];
            return location.index === column.length - 1 || this.isValidSequenceFromIndex(column, location.index);
        }

        return false;
    }

    getMovableSequence(cardInfo) {
        const { card, location } = cardInfo;

        if (location.type !== 'tableau') {
            return [card];
        }

        const column = this.tableau[location.column];
        const sequence = [];

        for (let i = location.index; i < column.length; i++) {
            sequence.push(column[i]);
        }

        const maxMovable = this.getMaxMovableCards();
        if (sequence.length > maxMovable) {
            return [card];
        }

        return sequence;
    }

    getMaxMovableCards() {
        const freeCells = this.freeCells.filter(cell => cell === null).length;
        const emptyColumns = this.tableau.filter(col => col.length === 0).length;
        return (freeCells + 1) * Math.pow(2, emptyColumns);
    }

    isValidSequenceFromIndex(column, startIndex) {
        for (let i = startIndex; i < column.length - 1; i++) {
            const current = column[i];
            const next = column[i + 1];

            if (current.value !== next.value + 1 || current.color === next.color) {
                return false;
            }
        }
        return true;
    }

    getDropTarget(clientX, clientY) {
        const element = document.elementFromPoint(clientX, clientY);
        const $element = $(element);

        let $pile = $element.closest('.freecell-pile, .foundation-pile, .tableau-pile');
        if ($pile.length === 0) return null;

        if ($pile.hasClass('freecell-pile')) {
            const index = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'freecell', index };
        }

        if ($pile.hasClass('foundation-pile')) {
            const suit = $pile.attr('data-suit');
            return { type: 'foundation', suit };
        }

        if ($pile.hasClass('tableau-pile')) {
            const column = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'tableau', column };
        }

        return null;
    }

    isValidDrop(dropTarget) {
        if (this.draggedCards.length === 0) return false;

        const firstCard = this.draggedCards[0];

        if (dropTarget.type === 'freecell') {
            return this.draggedCards.length === 1 && this.freeCells[dropTarget.index] === null;
        }

        if (dropTarget.type === 'foundation') {
            if (this.draggedCards.length !== 1) return false;
            if (firstCard.suit !== dropTarget.suit) return false;

            const foundationCards = this.foundation[dropTarget.suit];
            const expectedValue = foundationCards.length + 1;
            return firstCard.value === expectedValue;
        }

        if (dropTarget.type === 'tableau') {
            const targetColumn = this.tableau[dropTarget.column];

            if (targetColumn.length === 0) return true;

            const topCard = targetColumn[targetColumn.length - 1];
            return firstCard.value === topCard.value - 1 && firstCard.color !== topCard.color;
        }

        return false;
    }

    executeDrop(dropTarget) {
        this.isProcessingAction = true;

        this.saveMove();
        this.removeCardsFromSource();
        this.addCardsToDestination(dropTarget);

        this.moves++;
        if (dropTarget.type === 'foundation') {
            this.score += 10;
        } else {
            this.score += 1;
        }

        this.updateDisplay();

        if (this.checkWinCondition()) {
            this.onGameWon();
        }

        this.isProcessingAction = false;
    }

    removeCardsFromSource() {
        const location = this.draggedFrom;

        if (location.type === 'freecell') {
            this.freeCells[location.index] = null;
        } else if (location.type === 'tableau') {
            this.tableau[location.column].splice(location.index);
        }
    }

    addCardsToDestination(dropTarget) {
        if (dropTarget.type === 'freecell') {
            this.freeCells[dropTarget.index] = this.draggedCards[0];
        } else if (dropTarget.type === 'foundation') {
            this.foundation[dropTarget.suit].push(this.draggedCards[0]);
        } else if (dropTarget.type === 'tableau') {
            this.tableau[dropTarget.column].push(...this.draggedCards);
        }
    }

    saveMove() {
        const gameState = {
            tableau: JSON.parse(JSON.stringify(this.tableau)),
            freeCells: JSON.parse(JSON.stringify(this.freeCells)),
            foundation: JSON.parse(JSON.stringify(this.foundation)),
            score: this.score,
            moves: this.moves
        };
        this.moveHistory.push(gameState);

        if (this.moveHistory.length > 50) {
            this.moveHistory.shift();
        }
    }

    checkWinCondition() {
        for (let suit of this.suits) {
            if (this.foundation[suit].length !== 13) {
                return false;
            }
        }
        return true;
    }

    onGameWon() {
        this.gameWon = true;
        this.stopTimer();

        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        this.score += 500;
        this.updateStats();

        setTimeout(() => {
            this.showMessage('Congratulations!', 'You won the game!', {
                score: this.score,
                time: timeString,
                moves: this.moves
            });
        }, 1000);
    }

    onCardDoubleClick(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        e.preventDefault();
        const $card = $(e.currentTarget);
        const cardId = parseInt($card.attr('data-id'));

        const cardInfo = this.findCard(cardId);
        if (!cardInfo) return;

        this.tryAutoMoveToFoundation(cardInfo) || this.tryAutoMoveToTableau(cardInfo);
    }

    tryAutoMoveToFoundation(cardInfo) {
        const { card } = cardInfo;

        const foundationCards = this.foundation[card.suit];
        const expectedValue = foundationCards.length + 1;

        if (card.value === expectedValue) {
            this.draggedCards = [card];
            this.draggedFrom = cardInfo.location;

            const dropTarget = { type: 'foundation', suit: card.suit };
            this.executeDrop(dropTarget);
            this.resetDragState();
            return true;
        }

        return false;
    }

    tryAutoMoveToTableau(cardInfo) {
        const { card } = cardInfo;

        for (let col = 0; col < 8; col++) {
            const column = this.tableau[col];

            if (cardInfo.location.type === 'tableau' && cardInfo.location.column === col) {
                continue;
            }

            if (column.length === 0 ||
                (card.value === column[column.length - 1].value - 1 &&
                 card.color !== column[column.length - 1].color)) {

                this.draggedCards = [card];
                this.draggedFrom = cardInfo.location;

                const dropTarget = { type: 'tableau', column: col };
                this.executeDrop(dropTarget);
                this.resetDragState();
                return true;
            }
        }

        for (let i = 0; i < 4; i++) {
            if (this.freeCells[i] === null) {
                this.draggedCards = [card];
                this.draggedFrom = cardInfo.location;

                const dropTarget = { type: 'freecell', index: i };
                this.executeDrop(dropTarget);
                this.resetDragState();
                return true;
            }
        }

        return false;
    }

    onPileClick(e) {
        // Handle pile clicks if needed
    }

    undoMove() {
        if (this.moveHistory.length === 0 || this.isAnimatingCard || this.isProcessingAction) return;

        const lastMove = this.moveHistory.pop();
        this.isProcessingAction = true;

        this.tableau = JSON.parse(JSON.stringify(lastMove.tableau));
        this.freeCells = JSON.parse(JSON.stringify(lastMove.freeCells));
        this.foundation = JSON.parse(JSON.stringify(lastMove.foundation));
        this.score = lastMove.score;
        this.moves = lastMove.moves;

        this.updateDisplay();
        this.isProcessingAction = false;
    }

    showHint() {
        // Simple hint implementation
        $('.card').removeClass('hint-highlight');

        // Look for cards that can be moved to foundation
        for (let col = 0; col < 8; col++) {
            const column = this.tableau[col];
            if (column.length > 0) {
                const topCard = column[column.length - 1];
                const foundationCards = this.foundation[topCard.suit];
                const expectedValue = foundationCards.length + 1;

                if (topCard.value === expectedValue) {
                    $(`.card[data-id="${topCard.id}"]`).addClass('hint-highlight');
                    setTimeout(() => $('.card').removeClass('hint-highlight'), 3000);
                    return;
                }
            }
        }
    }

    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    showMessage(title, text, stats = null) {
        $('#messageTitle').text(title);
        $('#messageText').text(text);

        if (stats) {
            $('#finalScore').text(stats.score);
            $('#finalTime').text(stats.time);
            $('#finalMoves').text(stats.moves);
        }

        $('#gameMessage').removeClass('hidden');
    }

    hideMessage() {
        $('#gameMessage').addClass('hidden');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    onKeyDown(e) {
        if (e.ctrlKey && e.key === 'z') {
            e.preventDefault();
            this.undoMove();
        } else if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.newGame();
        } else if (e.key === 'h' || e.key === 'H') {
            e.preventDefault();
            this.showHint();
        }
    }
}

// Initialize game when document is ready
$(document).ready(function() {
    window.game = new FreeCellSolitaire();
});